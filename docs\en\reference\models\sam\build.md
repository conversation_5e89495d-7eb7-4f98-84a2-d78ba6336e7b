---
description: Discover detailed instructions for building various Segment Anything Model (SAM) and Segment Anything Model 2 (SAM 2) architectures with Ultralytics, including SAM ViT and Mobile-SAM.
keywords: Ultralytics, SAM model, Segment Anything Model, SAM 2 model, Segment Anything Model 2,  SAM ViT, Mobile-SAM, model building, deep learning, AI
---

# Reference for `ultralytics/models/sam/build.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/build.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/build.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/build.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.sam.build.build_sam_vit_h

<br><br><hr><br>

## ::: ultralytics.models.sam.build.build_sam_vit_l

<br><br><hr><br>

## ::: ultralytics.models.sam.build.build_sam_vit_b

<br><br><hr><br>

## ::: ultralytics.models.sam.build.build_mobile_sam

<br><br><hr><br>

## ::: ultralytics.models.sam.build.build_sam2_t

<br><br><hr><br>

## ::: ultralytics.models.sam.build.build_sam2_s

<br><br><hr><br>

## ::: ultralytics.models.sam.build.build_sam2_b

<br><br><hr><br>

## ::: ultralytics.models.sam.build.build_sam2_l

<br><br><hr><br>

## ::: ultralytics.models.sam.build._build_sam

<br><br><hr><br>

## ::: ultralytics.models.sam.build._build_sam2

<br><br><hr><br>

## ::: ultralytics.models.sam.build.build_sam

<br><br>
