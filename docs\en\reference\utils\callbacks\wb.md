---
description: Learn how Ultralytics YOLO integrates with WandB using custom callbacks for logging metrics and visualizations.
keywords: Ultralytics, YOLO, WandB, callbacks, logging, metrics, visualizations, AI, machine learning
---

# Reference for `ultralytics/utils/callbacks/wb.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/wb.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/wb.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/wb.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.wb._custom_table

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.wb._plot_curve

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.wb._log_plots

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.wb.on_pretrain_routine_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.wb.on_fit_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.wb.on_train_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.wb.on_train_end

<br><br>
