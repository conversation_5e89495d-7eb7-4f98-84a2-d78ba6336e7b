---
description: Explore the TaskAlignedAssigner in Ultralytics YOLO. Learn about the TaskAlignedMetric and its applications in object detection.
keywords: Ultralytics, YOLO, TaskAlignedAssigner, object detection, machine learning, AI, Tal.py, PyTorch
---

# Reference for `ultralytics/utils/tal.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/tal.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/tal.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/tal.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.tal.TaskAlignedAssigner

<br><br><hr><br>

## ::: ultralytics.utils.tal.RotatedTaskAlignedAssigner

<br><br><hr><br>

## ::: ultralytics.utils.tal.make_anchors

<br><br><hr><br>

## ::: ultralytics.utils.tal.dist2bbox

<br><br><hr><br>

## ::: ultralytics.utils.tal.bbox2dist

<br><br><hr><br>

## ::: ultralytics.utils.tal.dist2rbox

<br><br>
