---
description: This page provides a detailed reference for the ObjectBlurrer class in the Ultralytics solutions package, which enables real-time blurring of detected objects in images and videos.
keywords: Ultralytics, ObjectBlurrer, object detection, blurring, real-time processing, Python, computer vision
---

# Reference for `ultralytics/solutions/object_blurrer.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/object_blurrer.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/object_blurrer.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/object_blurrer.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.object_blurrer.ObjectBlurrer

<br><br>
