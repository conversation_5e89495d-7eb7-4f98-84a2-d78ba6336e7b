yolo cfg=../default.yaml task=detect mode=train data=../yolo8_scar.yaml model=../yolov8n.yaml epochs=60 imgsz=640 batch=16 workers=1 name=yolov8n-scar

from ultralytics import YOLO
import torch
import os

def train_model():
    # 设置环境变量
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    # 检查CUDA是否可用
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")

    # 加载模型
    # 方式1：使用预训练模型（推荐）
    model = YOLO('./yolo11n.pt')

    # 方式2：从零开始训练
    # model = YOLO('./yolo11.yaml')

    # 开始训练
    results = model.train(
        data='./yolo11_scar.yaml',        # 数据配置文件
        epochs=60,                     # 训练轮数
        imgsz=640,                    # 图像大小
        batch=16,                     # 批次大小
        device=device,                # 训练设备
        workers=1,                    # 数据加载的工作进程数
        project='runs/train',         # 项目名称
        name='yolov11n-scar',          # 实验名称
        exist_ok=True,                # 允许覆盖已存在的实验目录
        pretrained=False,             # 不使用预训练权重 如果为True，则使用预训练权重 会下载官方yolo8n.pt的模型文件 
        optimizer='auto',             # 优化器选择
        lr0=0.01,
        lrf=0.01,
        momentum=0.937,
        weight_decay=0.0005,
        warmup_epochs=3.0,
        warmup_momentum=0.8,
        warmup_bias_lr=0.1,
        box=7.5,
        cls=0.5,
        dfl=1.5,
        hsv_h=0.015,
        hsv_s=0.7,
        hsv_v=0.4,
        degrees=0.0, 
        translate=0.1,
        scale=0.5,
        mosaic=1.0,
        mixup=0.0,
        copy_paste=0.0,
        patience=100,    # 早停
        close_mosaic=10, # 最后10个epoch关闭mosaic
        amp=True,
        cos_lr=False,
        deterministic=True,
        verbose=True,  # 显示详细信息
        seed=42,       # 随机种子
        half = True
    
    )

    # 打印训练结果
    print("训练完成！")

if __name__ == '__main__':
    train_model() 

#需要将训练好的模型复制到当前项目根目录下，如果当前目录下存在同名的文件就将同名文件删除之后再复制
import os
import shutil

# 训练好的模型路径
source_path = './runs/train/yolov11n-scar/weights/best.pt'

# 目标路径
target_path = './best.pt'

# 如果目标文件已存在则删除
if os.path.exists(target_path):
    os.remove(target_path)
    print(f'已删除已存在的模型文件: {target_path}')

# 复制模型文件
shutil.copy2(source_path, target_path)
print(f'模型文件已复制到: {target_path}')



from ultralytics import YOLO

yolo = YOLO("./best.pt")

result = yolo.predict("./assets/1_back.png", save=True)

#检测结果可视化
import matplotlib.pyplot as plt
import cv2 
%matplotlib inline

#模型输出通道时BGR 需要转换成RGB显示 以下提供两种方式转换
# plt.imshow(result[0].plot()[:,:,::-1])
plt.imshow(cv2.cvtColor(result[0].plot(), cv2.COLOR_BGR2RGB))

from ultralytics import YOLO

yolo = YOLO("./best.pt")
yolo.export(format = 'tflite')