---
description: Discover the Ultralytics VisionEye solution for object tracking and analysis. Learn how to initialize parameters, map vision points, and track objects in real-time.
keywords: Ultralytics, VisionEye, Object Tracking, Computer Vision, Real-time Analysis, Python, AI
---

# Reference for `ultralytics/solutions/vision_eye.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/vision_eye.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/vision_eye.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/vision_eye.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.vision_eye.VisionEye

<br><br>
