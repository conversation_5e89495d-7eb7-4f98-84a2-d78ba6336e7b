---
description: Configure and customize Ultralytics Vision AI solutions using the SolutionConfig class. Define model paths, regions of interest, visualization options, tracking parameters, and keypoint analytics with a clean, type-safe dataclass structure for scalable development.
keywords: Ultralytics, SolutionConfig, vision AI configuration, YOLO models, Python dataclass, object detection, region of interest, tracking, keypoint analytics, computer vision, model inference, object counting, heatmaps, parking management, research
---

# Reference for `ultralytics/solutions/config.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/config.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/config.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/config.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.config.SolutionConfig

<br><br>
