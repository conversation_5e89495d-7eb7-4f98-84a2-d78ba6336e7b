---
description: Documentation for YOLOE segmentation trainer classes in Ultralytics, supporting different training approaches including standard training, linear probing, training from scratch, and visual prompt training.
keywords: YOLOE, segmentation, trainers, YOLOESegTrainer, YOLOEPESegTrainer, YOLOESegTrainerFromScratch, YOLOESegVPTrainer, linear probing, visual prompts, Ultralytics, deep learning
---

# Reference for `ultralytics/models/yolo/yoloe/train_seg.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/yoloe/train_seg.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/yoloe/train_seg.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/yoloe/train_seg.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.yoloe.train_seg.YOLOESegTrainer

<br><br><hr><br>

## ::: ultralytics.models.yolo.yoloe.train_seg.YOLOEPESegTrainer

<br><br><hr><br>

## ::: ultralytics.models.yolo.yoloe.train_seg.YOLOESegTrainerFromScratch

<br><br><hr><br>

## ::: ultralytics.models.yolo.yoloe.train_seg.YOLOESegVPTrainer

<br><br>
