# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Ultralytics YOLOv8 object detection model with P3/8 - P6/64 outputs
# Model docs: https://docs.ultralytics.com/models/yolov8
# Task docs: https://docs.ultralytics.com/tasks/detect
# Employs Ghost convolutions and modules proposed in Huawei's GhostNet in https://arxiv.org/abs/1911.11907v2

# Parameters
nc: 80 # number of classes
scales: # model compound scaling constants, i.e. 'model=yolov8n-p6.yaml' will call yolov8-p6.yaml with scale 'n'
  # [depth, width, max_channels]
  n: [0.33, 0.25, 1024] # YOLOv8n-ghost-p6 summary: 312 layers, 2901100 parameters, 2901084 gradients, 5.8 GFLOPs
  s: [0.33, 0.50, 1024] # YOLOv8s-ghost-p6 summary: 312 layers, 9520008 parameters, 9519992 gradients, 16.4 GFLOPs
  m: [0.67, 0.75, 768] # YOLOv8m-ghost-p6 summary: 468 layers, 18002904 parameters, 18002888 gradients, 34.4 GFLOPs
  l: [1.00, 1.00, 512] # YOLOv8l-ghost-p6 summary: 624 layers, 21227584 parameters, 21227568 gradients, 55.3 GFLOPs
  x: [1.00, 1.25, 512] # YOLOv8x-ghost-p6 summary: 624 layers, 33057852 parameters, 33057836 gradients, 85.7 GFLOPs

# YOLOv8.0-ghost backbone
backbone:
  # [from, repeats, module, args]
  - [-1, 1, Conv, [64, 3, 2]] # 0-P1/2
  - [-1, 1, GhostConv, [128, 3, 2]] # 1-P2/4
  - [-1, 3, C3Ghost, [128, True]]
  - [-1, 1, GhostConv, [256, 3, 2]] # 3-P3/8
  - [-1, 6, C3Ghost, [256, True]]
  - [-1, 1, GhostConv, [512, 3, 2]] # 5-P4/16
  - [-1, 6, C3Ghost, [512, True]]
  - [-1, 1, GhostConv, [768, 3, 2]] # 7-P5/32
  - [-1, 3, C3Ghost, [768, True]]
  - [-1, 1, GhostConv, [1024, 3, 2]] # 9-P6/64
  - [-1, 3, C3Ghost, [1024, True]]
  - [-1, 1, SPPF, [1024, 5]] # 11

# YOLOv8.0-ghost-p6 head
head:
  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 8], 1, Concat, [1]] # cat backbone P5
  - [-1, 3, C3Ghost, [768]] # 14

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 6], 1, Concat, [1]] # cat backbone P4
  - [-1, 3, C3Ghost, [512]] # 17

  - [-1, 1, nn.Upsample, [None, 2, "nearest"]]
  - [[-1, 4], 1, Concat, [1]] # cat backbone P3
  - [-1, 3, C3Ghost, [256]] # 20 (P3/8-small)

  - [-1, 1, GhostConv, [256, 3, 2]]
  - [[-1, 17], 1, Concat, [1]] # cat head P4
  - [-1, 3, C3Ghost, [512]] # 23 (P4/16-medium)

  - [-1, 1, GhostConv, [512, 3, 2]]
  - [[-1, 14], 1, Concat, [1]] # cat head P5
  - [-1, 3, C3Ghost, [768]] # 26 (P5/32-large)

  - [-1, 1, GhostConv, [768, 3, 2]]
  - [[-1, 11], 1, Concat, [1]] # cat head P6
  - [-1, 3, C3Ghost, [1024]] # 29 (P6/64-xlarge)

  - [[20, 23, 26, 29], 1, Detect, [nc]] # Detect(P3, P4, P5, P6)
