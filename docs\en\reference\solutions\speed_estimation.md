---
description: Explore the Ultralytics YOLO-based speed estimation script for real-time object tracking and speed measurement, optimized for accuracy and performance.
keywords: Ultralytics, speed estimation, YOLO, real-time tracking, object tracking, python
---

# Reference for `ultralytics/solutions/speed_estimation.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/speed_estimation.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/solutions/speed_estimation.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/solutions/speed_estimation.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.solutions.speed_estimation.SpeedEstimator

<br><br>
