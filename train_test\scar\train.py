from ultralytics import YOLO
import torch
import os

def train_model():
    # 设置环境变量
    os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
    # 检查CUDA是否可用
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")

    # 加载模型
    # 方式1：使用预训练模型（推荐）
    #model = YOLO('yolov8n.pt')
    
    # 方式2：从零开始训练
    model = YOLO('./yolo11.yaml')

    # 开始训练
    results = model.train(
        data='./yolo11_scar.yaml',        # 数据配置文件
        cfg='./default.yaml',
        epochs=60,                     # 训练轮数
        imgsz=640,                    # 图像大小
        batch=16,                     # 批次大小
        device=device,                # 训练设备
        workers=1,                    # 数据加载的工作进程数
        project='runs/train',         # 项目名称
        name='yolov11n-scar',          # 实验名称
        exist_ok=True,                # 允许覆盖已存在的实验目录
        pretrained=False,             # 不使用预训练权重 如果为True，则使用预训练权重 会下载官方yolo8n.pt的模型文件
        optimizer='auto',             # 优化器选择
        verbose=True,                 # 显示详细信息
        seed=0,                       # 随机种子
        deterministic=True,           # 使用确定性算法
        amp=True,                     # 使用自动混合精度训练
        fraction=1.0,                 # 数据集使用比例
        val=True,                     # 是否进行验证
        save=True,                    # 保存最佳模型
        save_period=10,               # 每隔多少轮保存一次
        patience=50,                  # 早停耐心值
        plots=True,                   # 保存训练图表
        cos_lr=True,                  # 使用余弦学习率调度
        close_mosaic=10,              # 最后多少轮关闭马赛克增强
        resume=False,                 # 是否从断点继续训练
    )

    # 打印训练结果
    print("训练完成！")

if __name__ == '__main__':
    train_model() 