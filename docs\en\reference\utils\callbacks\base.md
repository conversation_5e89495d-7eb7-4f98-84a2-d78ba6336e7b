---
description: Discover the essential base callbacks in Ultralytics for training, validation, prediction, and exporting models efficiently.
keywords: Ultralytics, base callbacks, training, validation, prediction, model export, ML, machine learning, deep learning
---

# Reference for `ultralytics/utils/callbacks/base.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/base.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/utils/callbacks/base.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/utils/callbacks/base.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.utils.callbacks.base.on_pretrain_routine_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_pretrain_routine_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_train_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_train_epoch_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_train_batch_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.optimizer_step

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_before_zero_grad

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_train_batch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_train_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_fit_epoch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_model_save

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_train_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_params_update

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.teardown

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_val_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_val_batch_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_val_batch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_val_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_predict_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_predict_batch_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_predict_batch_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_predict_postprocess_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_predict_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_export_start

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.on_export_end

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.get_default_callbacks

<br><br><hr><br>

## ::: ultralytics.utils.callbacks.base.add_integration_callbacks

<br><br>
