---
description: Explore detailed documentation on Ultralytics data loaders including SourceTypes, LoadStreams, and more. Enhance your ML workflows with our comprehensive guides.
keywords: Ultralytics, data loaders, SourceTypes, LoadStreams, LoadScreenshots, LoadImagesAndVideos, LoadPilAndNumpy, LoadTensor, ML workflows
---

# Reference for `ultralytics/data/loaders.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/loaders.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/data/loaders.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/data/loaders.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.data.loaders.SourceTypes

<br><br><hr><br>

## ::: ultralytics.data.loaders.LoadStreams

<br><br><hr><br>

## ::: ultralytics.data.loaders.LoadScreenshots

<br><br><hr><br>

## ::: ultralytics.data.loaders.LoadImagesAndVideos

<br><br><hr><br>

## ::: ultralytics.data.loaders.LoadPilAndNumpy

<br><br><hr><br>

## ::: ultralytics.data.loaders.LoadTensor

<br><br><hr><br>

## ::: ultralytics.data.loaders.autocast_list

<br><br><hr><br>

## ::: ultralytics.data.loaders.get_best_youtube_url

<br><br>
