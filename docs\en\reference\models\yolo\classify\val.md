---
description: Explore the source code and functionalities of the YOLO Classification Validator in Ultralytics for evaluating classification models effectively.
keywords: Ultralytics, YOLO, classification, validation, ClassifyMetrics, ConfusionMatrix, PyTorch, deep learning, model evaluation, AI, machine learning
---

# Reference for `ultralytics/models/yolo/classify/val.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/classify/val.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/yolo/classify/val.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/yolo/classify/val.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.yolo.classify.val.ClassificationValidator

<br><br>
