---
description: Explore the BYTETracker module in Ultralytics for state-of-the-art object tracking using Kalman filtering. Learn about its classes, methods, and attributes.
keywords: Ultralytics, BYTETracker, object tracking, <PERSON><PERSON> filter, YOLOv8, documentation
---

# Reference for `ultralytics/trackers/byte_tracker.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/byte_tracker.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/trackers/byte_tracker.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/trackers/byte_tracker.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.trackers.byte_tracker.STrack

<br><br><hr><br>

## ::: ultralytics.trackers.byte_tracker.BYTETracker

<br><br>
