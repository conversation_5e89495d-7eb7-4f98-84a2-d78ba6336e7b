| Argument       | Type        | Default | Description                                                                                                                                                                                                                                               |
| -------------- | ----------- | ------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `data`         | `str`       | `None`  | Specifies the path to the dataset configuration file (e.g., `coco8.yaml`). This file includes paths to [validation data](https://www.ultralytics.com/glossary/validation-data), class names, and number of classes.                                       |
| `imgsz`        | `int`       | `640`   | Defines the size of input images. All images are resized to this dimension before processing. Larger sizes may improve accuracy for small objects but increase computation time.                                                                          |
| `batch`        | `int`       | `16`    | Sets the number of images per batch. Higher values utilize GPU memory more efficiently but require more VRAM. Adjust based on available hardware resources.                                                                                               |
| `save_json`    | `bool`      | `False` | If `True`, saves the results to a JSON file for further analysis, integration with other tools, or submission to evaluation servers like COCO.                                                                                                            |
| `conf`         | `float`     | `0.001` | Sets the minimum confidence threshold for detections. Lower values increase recall but may introduce more false positives. Used during [validation](https://docs.ultralytics.com/modes/val/) to compute precision-recall curves.                          |
| `iou`          | `float`     | `0.7`   | Sets the [Intersection Over Union](https://www.ultralytics.com/glossary/intersection-over-union-iou) threshold for [Non-Maximum Suppression](https://www.ultralytics.com/glossary/non-maximum-suppression-nms). Controls duplicate detection elimination. |
| `max_det`      | `int`       | `300`   | Limits the maximum number of detections per image. Useful in dense scenes to prevent excessive detections and manage computational resources.                                                                                                             |
| `half`         | `bool`      | `True`  | Enables half-[precision](https://www.ultralytics.com/glossary/precision) (FP16) computation, reducing memory usage and potentially increasing speed with minimal impact on [accuracy](https://www.ultralytics.com/glossary/accuracy).                     |
| `device`       | `str`       | `None`  | Specifies the device for validation (`cpu`, `cuda:0`, etc.). When `None`, automatically selects the best available device. Multiple CUDA devices can be specified with comma separation.                                                                  |
| `dnn`          | `bool`      | `False` | If `True`, uses the [OpenCV](https://www.ultralytics.com/glossary/opencv) DNN module for ONNX model inference, offering an alternative to [PyTorch](https://www.ultralytics.com/glossary/pytorch) inference methods.                                      |
| `plots`        | `bool`      | `False` | When set to `True`, generates and saves plots of predictions versus ground truth, confusion matrices, and PR curves for visual evaluation of model performance.                                                                                           |
| `classes`      | `list[int]` | `None`  | Specifies a list of class IDs to train on. Useful for filtering out and focusing only on certain classes during evaluation.                                                                                                                               |
| `rect`         | `bool`      | `True`  | If `True`, uses rectangular inference for batching, reducing padding and potentially increasing speed and efficiency by processing images in their original aspect ratio.                                                                                 |
| `split`        | `str`       | `'val'` | Determines the dataset split to use for validation (`val`, `test`, or `train`). Allows flexibility in choosing the data segment for performance evaluation.                                                                                               |
| `project`      | `str`       | `None`  | Name of the project directory where validation outputs are saved. Helps organize results from different experiments or models.                                                                                                                            |
| `name`         | `str`       | `None`  | Name of the validation run. Used for creating a subdirectory within the project folder, where validation logs and outputs are stored.                                                                                                                     |
| `verbose`      | `bool`      | `False` | If `True`, displays detailed information during the validation process, including per-class metrics, batch progress, and additional debugging information.                                                                                                |
| `save_txt`     | `bool`      | `False` | If `True`, saves detection results in text files, with one file per image, useful for further analysis, custom post-processing, or integration with other systems.                                                                                        |
| `save_conf`    | `bool`      | `False` | If `True`, includes confidence values in the saved text files when `save_txt` is enabled, providing more detailed output for analysis and filtering.                                                                                                      |
| `workers`      | `int`       | `8`     | Number of worker threads for data loading. Higher values can speed up data preprocessing but may increase CPU usage. Setting to 0 uses main thread, which can be more stable in some environments.                                                        |
| `augment`      | `bool`      | `False` | Enables test-time augmentation (TTA) during validation, potentially improving detection accuracy at the cost of inference speed by running inference on transformed versions of the input.                                                                |
| `agnostic_nms` | `bool`      | `False` | Enables class-agnostic [Non-Maximum Suppression](https://www.ultralytics.com/glossary/non-maximum-suppression-nms), which merges overlapping boxes regardless of their predicted class. Useful for instance-focused applications.                         |
| `single_cls`   | `bool`      | `False` | Treats all classes as a single class during validation. Useful for evaluating model performance on binary detection tasks or when class distinctions aren't important.                                                                                    |
| `visualize`    | `bool`      | `False` | Visualizes the ground truths, true positives, false positives and false negatives for each image. Useful for debugging and model interpretation.                                                                                                          |
