---
comments: true
description: Get started with Ultralytics HUB! Learn to upload datasets, train YOLO models, and manage projects easily with our user-friendly platform.
keywords: Ultralytics HUB, Quickstart, YOLO models, dataset upload, project management, train models, machine learning
---

# Ultralytics HUB Quickstart

[Ultralytics HUB](https://www.ultralytics.com/hub) is designed to be user-friendly and intuitive, allowing users to quickly upload their datasets and train new YOLO models. It also offers a range of pre-trained models to choose from, making it extremely easy for users to get started. Once a model is trained, it can be effortlessly previewed in the [Ultralytics HUB App](app/index.md) before being deployed for real-time classification, [object detection](https://www.ultralytics.com/glossary/object-detection), and [instance segmentation](https://www.ultralytics.com/glossary/instance-segmentation) tasks.

<p align="center">
  <iframe loading="lazy" width="720" height="405" src="https://www.youtube.com/embed/qE-dfbB5Sis"
    title="YouTube video player" frameborder="0"
    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
    allowfullscreen>
  </iframe>
  <br>
  <strong>Watch:</strong> How to train Ultralytics YOLO11 on Custom Dataset using Ultralytics HUB | HUB Datasets 🚀
</p>

## Get Started

[Ultralytics HUB](https://www.ultralytics.com/hub) offers a variety of easy signup options. You can register and log in using your Google, Apple, or GitHub accounts, or simply with your email address.

![Ultralytics HUB screenshot of the Signup page](https://github.com/ultralytics/docs/releases/download/0/ultralytics-hub-signup-page.avif)

During the signup, you will be asked to complete your profile.

![Ultralytics HUB screenshot of the Signup page profile form](https://github.com/ultralytics/docs/releases/download/0/ultralytics-hub-signup-profile-form.avif)

??? tip

    You can update your profile from the [Account](https://hub.ultralytics.com/settings?tab=account) tab on the [Settings](https://hub.ultralytics.com/settings) page.

    ![Ultralytics HUB screenshot of the Settings page Account tab with an arrow pointing to the Profile card](https://github.com/ultralytics/docs/releases/download/0/hub-settings-account-profile.avif)

## Home

After signing in, you will be directed to the [Home](https://hub.ultralytics.com/home) page of [Ultralytics HUB](https://www.ultralytics.com/hub), which provides a comprehensive overview, quick links, and updates.

The sidebar conveniently offers links to important modules of the platform, such as [Datasets](https://hub.ultralytics.com/datasets), [Projects](https://hub.ultralytics.com/projects), and [Models](https://hub.ultralytics.com/models).

![Ultralytics HUB screenshot of the Home page](https://github.com/ultralytics/docs/releases/download/0/hub-home.avif)

### Recent

You can easily search globally or directly access your last updated [Datasets](https://hub.ultralytics.com/datasets), [Projects](https://hub.ultralytics.com/projects), or [Models](https://hub.ultralytics.com/models) using the Recent card on the [Home](https://hub.ultralytics.com/home) page.

![Ultralytics HUB screenshot of the Home page with an arrow pointing to the Recent card](https://github.com/ultralytics/docs/releases/download/0/hub-recent-card.avif)

### Upload Dataset

You can upload a dataset directly from the [Home](https://hub.ultralytics.com/home) page. Ultralytics HUB supports various dataset formats and makes it easy to prepare your data for [training custom YOLO models](https://www.ultralytics.com/blog/how-to-train-and-deploy-yolo11-using-ultralytics-hub).

![Ultralytics HUB screenshot of the Home page with an arrow pointing to the Upload Dataset card](https://github.com/ultralytics/docs/releases/download/0/ultralytics-hub-upload-dataset-card.avif)

Read more about [datasets](datasets.md) and how to prepare them for optimal training results.

### Create Project

You can create a project directly from the [Home](https://hub.ultralytics.com/home) page. Projects help you organize related models and experiments in one place, making it easier to track progress and compare results.

![Ultralytics HUB screenshot of the Home page with an arrow pointing to the Create Project card](https://github.com/ultralytics/docs/releases/download/0/hub-create-project-card.avif)

Read more about [projects](projects.md) and how they can streamline your workflow.

### Train Model

You can train a model directly from the [Home](https://hub.ultralytics.com/home) page. Ultralytics HUB offers multiple training options including [cloud training](cloud-training.md), Google Colab integration, or using your own hardware.

![Ultralytics HUB screenshot of the Home page with an arrow pointing to the Train Model card](https://github.com/ultralytics/docs/releases/download/0/ultralytics-hub-train-model-card.avif)

Read more about [models](models.md) and the various architectures available for your computer vision tasks.

## Feedback

We value your feedback! Feel free to leave a review at any time to help us improve the platform.

![Ultralytics HUB screenshot of the Home page with an arrow pointing to the Feedback button](https://github.com/ultralytics/docs/releases/download/0/hub-feedback-button.avif)

![Ultralytics HUB screenshot of the Feedback dialog](https://github.com/ultralytics/docs/releases/download/0/ultralytics-hub-feedback-dialog.avif)

??? info

    Only our team will see your feedback, and we will use it to improve our platform.

## Need Help?

If you encounter any issues or have questions, we're here to assist you.

You can report a bug, request a feature, or ask a question on [GitHub](https://github.com/ultralytics/hub/issues/new/choose).

!!! note

    When reporting a bug, please include your Environment Details from the [Support](https://hub.ultralytics.com/support) page.

    ![Ultralytics HUB screenshot of the Support page with an arrow pointing to Support button in the sidebar and one to the Copy Environment Details button](https://github.com/ultralytics/docs/releases/download/0/hub-support-page.avif)

??? tip

    You can join our [Discord](https://discord.com/invite/ultralytics) community for questions and discussions!
